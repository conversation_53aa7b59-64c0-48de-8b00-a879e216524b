import 'dotenv/config';
import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import helmet from 'helmet';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

// ES module equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Importar módulos de autenticação
import config from './config.js';
import SystemInitializer from './utils/initializeSystem.js';
import { generalLimiter } from './middleware/rateLimiter.js';

// Importar rotas
import authRoutes from './routes/auth.js';
import usersRoutes from './routes/users.js';
import organizationsRoutes from './routes/organizations.js';
import aiRoutes from './routes/ai.js';
import stripeRoutes from './routes/stripe.js';
import analyticsRoutes from './routes/analytics.js';
import notificationsRoutes from './routes/notifications.js';
import NotificationMiddleware from './middleware/notificationMiddleware.js';

const app = express();
const PORT = process.env.PORT || 3002;

// Middleware de segurança
app.use(helmet({
  contentSecurityPolicy: false, // Desabilitar CSP para o dashboard funcionar
  crossOriginEmbedderPolicy: false
}));

// Configurar origens permitidas para CORS
const getAllowedOrigins = () => {
  const origins = [
    // URLs de desenvolvimento
    'http://localhost:3000',
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:3002',
    'http://localhost:8080',
    'http://127.0.0.1:5500', // Para Live Server
    'http://localhost:5500',  // Para Live Server alternativo
    'http://127.0.0.1:8080',  // Para outros servidores locais
    'http://localhost:8000',  // Para Python server
    'null', // Para arquivos locais

    // URLs de produção
    'https://promandato-9a4cf.web.app',
    'https://promandato-9a4cf.firebaseapp.com',
    'https://promandato-backend-2h6tsoanrq-rj.a.run.app',
    'https://app.promandato.com.br',
    'https://promandato.com.br',
    'https://www.promandato.com.br'
  ];

  // Adicionar URLs do ambiente se definidas
  if (process.env.FRONTEND_URL) {
    const envUrls = process.env.FRONTEND_URL.split(',').map(url => url.trim());
    origins.push(...envUrls);
  }

  return origins;
};

// Middleware de debug para CORS
app.use((req, res, next) => {
  const origin = req.headers.origin;
  if (process.env.NODE_ENV === 'development' || process.env.DEBUG_CORS === 'true') {
    console.log(`[CORS DEBUG] ${req.method} ${req.path} from origin: ${origin || 'no-origin'}`);
  }
  next();
});

// Middleware básico
app.use(cors({
  origin: function (origin, callback) {
    const allowedOrigins = getAllowedOrigins();

    // Permitir requests sem origin (mobile apps, Postman, etc.)
    if (!origin) {
      console.log('[CORS] Allowing request without origin');
      return callback(null, true);
    }

    if (allowedOrigins.indexOf(origin) !== -1 || allowedOrigins.includes('*')) {
      console.log(`[CORS] Allowing origin: ${origin}`);
      callback(null, true);
    } else {
      console.log(`[CORS] BLOCKED origin: ${origin}`);
      console.log(`[CORS] Allowed origins:`, allowedOrigins);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['Content-Length', 'X-Foo', 'X-Bar'],
  maxAge: 86400 // 24 hours
}));

app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

// CORS middleware para endpoints públicos
app.use('/api/public', (req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// CORS específico para Stripe e Analytics (endpoints usados pela landing page)
app.use(['/api/stripe', '/api/analytics'], (req, res, next) => {
  const allowedOrigins = [
    // URLs de desenvolvimento
    'http://localhost:3002',
    'http://localhost:5173',
    'http://localhost:5174',
    'http://127.0.0.1:5500',
    'http://localhost:5500',
    'http://127.0.0.1:8080',
    'http://localhost:8080',
    'http://localhost:8000',
    'null', // Para arquivos locais

    // URLs de produção
    'https://promandato-9a4cf.web.app',
    'https://promandato-9a4cf.firebaseapp.com',
    'https://promandato-backend-2h6tsoanrq-rj.a.run.app',
    'https://app.promandato.com.br',
    'https://promandato.com.br',
    'https://www.promandato.com.br'
  ];

  const origin = req.headers.origin;
  if (allowedOrigins.includes(origin) || !origin) {
    res.header('Access-Control-Allow-Origin', origin || '*');
  }

  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  res.header('Access-Control-Allow-Credentials', 'true');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Health check endpoints (antes do rate limiting)
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Promandato Backend API',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    status: 'running'
  });
});

app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Servidor funcionando',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    status: 'healthy'
  });
});

// Rate limiting geral (aplicado após health checks)
app.use(generalLimiter);

// Configurar Express para confiar em proxies (Cloud Run)
app.set('trust proxy', true); // Confiar em todos os proxies para Cloud Run

// Servir arquivos estáticos do dashboard com configuração melhorada
app.use(express.static(path.join(__dirname, 'dashboard/dist'), {
  maxAge: process.env.NODE_ENV === 'production' ? '1d' : '0',
  etag: true,
  lastModified: true,
  setHeaders: (res, filePath) => {
    // Configurar MIME types corretamente
    if (filePath.endsWith('.css')) {
      res.setHeader('Content-Type', 'text/css; charset=utf-8');
    } else if (filePath.endsWith('.js')) {
      res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
    } else if (filePath.endsWith('.html')) {
      res.setHeader('Content-Type', 'text/html; charset=utf-8');
    } else if (filePath.endsWith('.json')) {
      res.setHeader('Content-Type', 'application/json; charset=utf-8');
    } else if (filePath.endsWith('.ico')) {
      res.setHeader('Content-Type', 'image/x-icon');
    } else if (filePath.endsWith('.png')) {
      res.setHeader('Content-Type', 'image/png');
    } else if (filePath.endsWith('.jpg') || filePath.endsWith('.jpeg')) {
      res.setHeader('Content-Type', 'image/jpeg');
    } else if (filePath.endsWith('.svg')) {
      res.setHeader('Content-Type', 'image/svg+xml');
    }

    // Headers de segurança para arquivos estáticos
    res.setHeader('X-Content-Type-Options', 'nosniff');

    // Cache control para produção
    if (process.env.NODE_ENV === 'production') {
      if (filePath.includes('/assets/')) {
        res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
      } else {
        res.setHeader('Cache-Control', 'public, max-age=86400');
      }
    }
  }
}));

// Servir arquivos estáticos da landing page com configuração melhorada
app.use('/landingpage', express.static(path.join(__dirname, '../landingpage'), {
  maxAge: process.env.NODE_ENV === 'production' ? '1d' : '0',
  etag: true,
  lastModified: true,
  setHeaders: (res, filePath) => {
    // Configurar MIME types corretamente
    if (filePath.endsWith('.css')) {
      res.setHeader('Content-Type', 'text/css; charset=utf-8');
    } else if (filePath.endsWith('.js')) {
      res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
    } else if (filePath.endsWith('.html')) {
      res.setHeader('Content-Type', 'text/html; charset=utf-8');
    } else if (filePath.endsWith('.json')) {
      res.setHeader('Content-Type', 'application/json; charset=utf-8');
    } else if (filePath.endsWith('.ico')) {
      res.setHeader('Content-Type', 'image/x-icon');
    } else if (filePath.endsWith('.png')) {
      res.setHeader('Content-Type', 'image/png');
    } else if (filePath.endsWith('.jpg') || filePath.endsWith('.jpeg')) {
      res.setHeader('Content-Type', 'image/jpeg');
    } else if (filePath.endsWith('.svg')) {
      res.setHeader('Content-Type', 'image/svg+xml');
    }

    // Headers de segurança
    res.setHeader('X-Content-Type-Options', 'nosniff');

    // Cache control para produção
    if (process.env.NODE_ENV === 'production') {
      res.setHeader('Cache-Control', 'public, max-age=86400');
    }
  }
}));

// Rotas da API
app.use('/api/auth', authRoutes);
app.use('/api/users', usersRoutes);
app.use('/api/organizations', organizationsRoutes);
app.use('/api/ai', aiRoutes);
app.use('/api/stripe', stripeRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/notifications', notificationsRoutes);

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Servidor funcionando',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// CORS debug endpoint
app.get('/api/cors-debug', (req, res) => {
  const origin = req.headers.origin;
  const allowedOrigins = getAllowedOrigins();

  res.json({
    success: true,
    message: 'CORS Debug Information',
    data: {
      requestOrigin: origin || 'no-origin',
      allowedOrigins: allowedOrigins,
      isOriginAllowed: !origin || allowedOrigins.includes(origin),
      environment: process.env.NODE_ENV,
      frontendUrl: process.env.FRONTEND_URL,
      corsOrigin: process.env.CORS_ORIGIN
    },
    timestamp: new Date().toISOString()
  });
});

// Informações do sistema (público)
app.get('/api/system/info', async (req, res) => {
  try {
    const systemInfo = await SystemInitializer.getSystemInfo();
    res.json({
      success: true,
      data: {
        ...systemInfo,
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development'
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao obter informações do sistema'
    });
  }
});

// Caminho para o arquivo de configuração dos planos
const PLANS_CONFIG_PATH = path.join(__dirname, '../types/plans.ts');
const PLANS_DATA_PATH = path.join(__dirname, 'data/plans.json');

// Garantir que o diretório de dados existe
fs.ensureDirSync(path.dirname(PLANS_DATA_PATH));

// Dados iniciais dos planos (caso o arquivo não exista)
const DEFAULT_PLANS = [
  {
    id: 'BASIC',
    name: 'Básico',
    description: 'Ideal para pequenas equipes e projetos iniciais',
    price: {
      monthly: 169.90,
      yearly: 1834.92
    },
    features: {
      maxUsers: 5,
      maxDemands: 100,
      maxCitizens: 500,
      storageGB: 10,
      bulkMessages: false,
      socialMediaIntegration: false,
      smsNotifications: false,
      advancedReports: false,
      customReports: false,
      dataExport: true,
      apiAccess: false,
      webhooks: false,
      supportLevel: 'basic',
      aiFeatures: []
    },
    enabled: true,
    popular: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'STANDARD',
    name: 'Padrão',
    description: 'Para equipes em crescimento que precisam de mais recursos',
    price: {
      monthly: 259.90,
      yearly: 2807.08
    },
    features: {
      maxUsers: 15,
      maxDemands: 500,
      maxCitizens: 2000,
      storageGB: 50,
      bulkMessages: true,
      socialMediaIntegration: true,
      smsNotifications: false,
      advancedReports: true,
      customReports: false,
      dataExport: true,
      apiAccess: true,
      webhooks: false,
      supportLevel: 'priority',
      aiFeatures: [
        {
          id: 'text-analysis',
          name: 'Análise de Texto',
          description: 'Análise automática de sentimentos em demandas',
          category: 'Análise',
          enabled: true
        }
      ]
    },
    enabled: true,
    popular: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'PROFESSIONAL',
    name: 'Profissional',
    description: 'Solução completa com IA avançada para grandes organizações',
    price: {
      monthly: 599.90,
      yearly: 6478.92
    },
    features: {
      maxUsers: -1,
      maxDemands: -1,
      maxCitizens: -1,
      storageGB: 200,
      bulkMessages: true,
      socialMediaIntegration: true,
      smsNotifications: true,
      advancedReports: true,
      customReports: true,
      dataExport: true,
      apiAccess: true,
      webhooks: true,
      supportLevel: 'dedicated',
      aiFeatures: [
        {
          id: 'text-analysis',
          name: 'Análise de Texto',
          description: 'Análise automática de sentimentos em demandas',
          category: 'Análise',
          enabled: true
        },
        {
          id: 'auto-categorization',
          name: 'Categorização Automática',
          description: 'Categorização inteligente de demandas',
          category: 'Automação',
          enabled: true
        },
        {
          id: 'predictive-analytics',
          name: 'Análise Preditiva',
          description: 'Previsões baseadas em dados históricos',
          category: 'Análise',
          enabled: true
        },
        {
          id: 'smart-responses',
          name: 'Respostas Inteligentes',
          description: 'Sugestões automáticas de respostas',
          category: 'Automação',
          enabled: true
        }
      ]
    },
    enabled: true,
    popular: false,
    badge: 'IA Incluída',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// Função para carregar os planos
function loadPlans() {
  try {
    if (fs.existsSync(PLANS_DATA_PATH)) {
      return JSON.parse(fs.readFileSync(PLANS_DATA_PATH, 'utf8'));
    } else {
      // Criar arquivo inicial
      fs.writeFileSync(PLANS_DATA_PATH, JSON.stringify(DEFAULT_PLANS, null, 2));
      return DEFAULT_PLANS;
    }
  } catch (error) {
    console.error('Erro ao carregar planos:', error);
    return DEFAULT_PLANS;
  }
}

// Função para salvar os planos
function savePlans(plans) {
  try {
    fs.writeFileSync(PLANS_DATA_PATH, JSON.stringify(plans, null, 2));
    return true;
  } catch (error) {
    console.error('Erro ao salvar planos:', error);
    return false;
  }
}

// Função para extrair todas as features de IA únicas dos planos
function extractAllAIFeatures(plans) {
  const featuresMap = new Map();
  
  plans.forEach(plan => {
    if (plan.features && plan.features.aiFeatures) {
      plan.features.aiFeatures.forEach(feature => {
        if (!featuresMap.has(feature.id)) {
          featuresMap.set(feature.id, feature);
        }
      });
    }
  });
  
  return Array.from(featuresMap.values());
}


// Função para atualizar o arquivo TypeScript
function updateTypescriptFile(plans) {
  try {
    const allAIFeatures = extractAllAIFeatures(plans);
    
    const tsContent = `// Este arquivo é gerado automaticamente pelo dashboard de administração
// Não edite manualmente - use o dashboard para fazer alterações

export enum PlanType {
  BASIC = 'BASIC',
  STANDARD = 'STANDARD',
  PROFESSIONAL = 'PROFESSIONAL'
}

export interface PlanPrice {
  monthly: number;
  yearly: number;
}

export interface AIFeature {
  id: string;
  name: string;
  description: string;
  category: string;
  enabled: boolean;
}

export interface PlanFeatures {
  maxUsers: number; // -1 para ilimitado
  maxDemands: number; // -1 para ilimitado
  maxCitizens: number; // -1 para ilimitado
  storageGB: number;
  bulkMessages: boolean;
  socialMediaIntegration: boolean;
  smsNotifications: boolean;
  advancedReports: boolean;
  customReports: boolean;
  dataExport: boolean;
  apiAccess: boolean;
  webhooks: boolean;
  supportLevel: 'basic' | 'priority' | 'dedicated';
  aiFeatures: AIFeature[];
}

export interface Plan {
  id: PlanType;
  name: string;
  description: string;
  price: PlanPrice;
  features: PlanFeatures;
  enabled: boolean;
  popular?: boolean;
  badge?: string;
  createdAt: string;
  updatedAt: string;
}

// Lista completa de features de IA disponíveis
export const AI_FEATURES: AIFeature[] = ${JSON.stringify(allAIFeatures, null, 2)};

// Configuração atual dos planos (atualizada automaticamente)
export const PLANS: Plan[] = ${JSON.stringify(plans, null, 2)};

// Função para obter um plano por ID
export function getPlanById(planId: PlanType): Plan | undefined {
  return PLANS.find(plan => plan.id === planId);
}

// Função para obter apenas planos ativos
export function getActivePlans(): Plan[] {
  return PLANS.filter(plan => plan.enabled);
}

// Última atualização
export const LAST_UPDATED = '${new Date().toISOString()}';
`;

    fs.writeFileSync(PLANS_CONFIG_PATH, tsContent);
    console.log('Arquivo TypeScript atualizado:', PLANS_CONFIG_PATH);
    return true;
  } catch (error) {
    console.error('Erro ao atualizar arquivo TypeScript:', error);
    return false;
  }
}

// Rotas da API

// GET /api/plans - Listar todos os planos
app.get('/api/plans', (req, res) => {
  try {
    const plans = loadPlans();
    res.json({
      success: true,
      data: plans,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao carregar planos',
      details: error.message
    });
  }
});

// GET /api/public/plans - Endpoint público para landing page
app.get('/api/public/plans', (req, res) => {
  try {
    const plans = loadPlans();

    // Filtrar apenas planos ativos e dados necessários para a landing page
    const publicPlans = plans
      .filter(plan => plan.enabled)
      .map(plan => ({
        id: plan.id,
        name: plan.name,
        description: plan.description,
        price: plan.price,
        popular: plan.popular || false,
        badge: plan.badge || null,
        features: {
          maxUsers: plan.features.maxUsers,
          maxDemands: plan.features.maxDemands,
          maxCitizens: plan.features.maxCitizens,
          storageGB: plan.features.storageGB,
          supportLevel: plan.features.supportLevel,
          aiFeatures: plan.features.aiFeatures || []
        }
      }));

    // Adicionar headers CORS para permitir acesso da landing page
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET');
    res.header('Access-Control-Allow-Headers', 'Content-Type');

    res.json({
      success: true,
      data: publicPlans,
      timestamp: new Date().toISOString(),
      cache: {
        maxAge: 300, // 5 minutos
        lastModified: new Date().toISOString()
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao carregar planos públicos',
      details: error.message
    });
  }
});

// POST /api/public/contact - Endpoint público para formulário de contato
app.post('/api/public/contact', (req, res) => {
  try {
    const { name, email, phone, organization, message } = req.body;

    // Validação básica
    if (!name || !email) {
      return res.status(400).json({
        success: false,
        error: 'Nome e email são obrigatórios'
      });
    }

    // Aqui você pode implementar:
    // - Salvar no banco de dados
    // - Enviar email
    // - Integrar com CRM
    console.log('Novo contato recebido:', { name, email, phone, organization, message });

    // Headers CORS
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'POST');
    res.header('Access-Control-Allow-Headers', 'Content-Type');

    res.json({
      success: true,
      message: 'Contato recebido com sucesso! Entraremos em contato em breve.',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao processar contato',
      details: error.message
    });
  }
});

// POST /api/public/demo - Endpoint público para solicitação de demo
app.post('/api/public/demo', (req, res) => {
  try {
    const { name, email, phone, organization, message } = req.body;

    // Validação básica
    if (!name || !email) {
      return res.status(400).json({
        success: false,
        error: 'Nome e email são obrigatórios'
      });
    }

    // Aqui você pode implementar:
    // - Agendar demonstração
    // - Enviar email de confirmação
    // - Integrar com calendário
    console.log('Nova solicitação de demo:', { name, email, phone, organization, message });

    // Headers CORS
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'POST');
    res.header('Access-Control-Allow-Headers', 'Content-Type');

    res.json({
      success: true,
      message: 'Solicitação de demonstração recebida! Entraremos em contato para agendar.',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao processar solicitação de demo',
      details: error.message
    });
  }
});

// PUT /api/plans/:planId - Atualizar um plano específico
app.put('/api/plans/:planId', (req, res) => {
  try {
    const { planId } = req.params;
    const updates = req.body;
    
    const plans = loadPlans();
    const planIndex = plans.findIndex(plan => plan.id === planId);
    
    if (planIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Plano não encontrado'
      });
    }
    
    // Atualizar o plano
    plans[planIndex] = {
      ...plans[planIndex],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    
    // Salvar alterações
    if (savePlans(plans)) {
      updateTypescriptFile(plans);
      res.json({
        success: true,
        data: plans[planIndex],
        message: 'Plano atualizado com sucesso'
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Erro ao salvar alterações'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao atualizar plano',
      details: error.message
    });
  }
});

// PUT /api/plans/bulk - Atualizar múltiplos planos
app.put('/api/plans/bulk', (req, res) => {
  try {
    const { updates } = req.body; // Array de { planId, updates }
    
    const plans = loadPlans();
    let updatedCount = 0;
    
    updates.forEach(({ planId, updates: planUpdates }) => {
      const planIndex = plans.findIndex(plan => plan.id === planId);
      if (planIndex !== -1) {
        plans[planIndex] = {
          ...plans[planIndex],
          ...planUpdates,
          updatedAt: new Date().toISOString()
        };
        updatedCount++;
      }
    });
    
    if (savePlans(plans)) {
      updateTypescriptFile(plans);
      res.json({
        success: true,
        data: plans,
        message: `${updatedCount} planos atualizados com sucesso`
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Erro ao salvar alterações'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao atualizar planos',
      details: error.message
    });
  }
});

// POST /api/plans/reset - Resetar para configuração padrão
app.post('/api/plans/reset', (req, res) => {
  try {
    if (savePlans(DEFAULT_PLANS)) {
      updateTypescriptFile(DEFAULT_PLANS);
      res.json({
        success: true,
        data: DEFAULT_PLANS,
        message: 'Planos resetados para configuração padrão'
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Erro ao resetar planos'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao resetar planos',
      details: error.message
    });
  }
});

// GET /api/health - Health check
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'API funcionando corretamente',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Middleware de tratamento de erros
app.use((error, req, res, next) => {
  console.error('Erro não tratado:', error);
  res.status(500).json({
    success: false,
    message: 'Erro interno do servidor',
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
  });
});

// Servir o dashboard React para todas as outras rotas
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dashboard/dist/index.html'));
});

// Função para inicializar o servidor
async function startServer() {
  try {
    console.log('🔧 Inicializando sistema...');
    
    // Inicializar sistema de autenticação
    await SystemInitializer.initialize();
    
    // Garantir que o arquivo TypeScript dos planos existe
    const plans = loadPlans();
    updateTypescriptFile(plans);

    // Configurar tarefas automáticas de notificação
    NotificationMiddleware.setupAutomaticTasks();

    // Iniciar servidor
    app.listen(PORT, () => {
      console.log('\n🎉 Sistema inicializado com sucesso!');
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
      console.log(`🚀 Servidor rodando na porta ${PORT}`);
      console.log(`📊 Dashboard disponível em: http://localhost:${PORT}`);
      console.log(`🔧 API disponível em: http://localhost:${PORT}/api`);
      console.log(`🔐 Autenticação disponível em: http://localhost:${PORT}/api/auth`);
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
      console.log('\n📋 Credenciais padrão:');
      console.log(`   Email: ${config.defaultAdmin.email}`);
      console.log(`   Senha: ${config.defaultAdmin.password}`);
      console.log('\n⚠️  IMPORTANTE: Altere a senha padrão após o primeiro login!');
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
    });
    
  } catch (error) {
    console.error('❌ Erro ao inicializar servidor:', error);
    process.exit(1);
  }
}

// Tratamento de sinais para shutdown graceful
process.on('SIGTERM', () => {
  console.log('🛑 Recebido SIGTERM, encerrando servidor...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('\n🛑 Recebido SIGINT, encerrando servidor...');
  process.exit(0);
});

// Tratamento de erros não capturados
process.on('uncaughtException', (error) => {
  console.error('❌ Erro não capturado:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promise rejeitada não tratada:', reason);
  process.exit(1);
});

// Iniciar servidor
startServer();

export default app;
