# Use Node.js 20 Alpine (mais recente e seguro)
FROM node:20-alpine

# Definir diretório de trabalho
WORKDIR /app

# Copiar arquivos de dependências
COPY package*.json ./

# Instalar dependências de produção
RUN npm ci --only=production && npm cache clean --force

# Copiar código fonte (excluindo node_modules)
COPY . .

# Build do dashboard se necessário
RUN npm run build || echo "Build não necessário"

# Criar usuário não-root para segurança
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Criar diretórios necessários
RUN mkdir -p /app/data /app/uploads && chown -R nodejs:nodejs /app

# Mudar para usuário não-root
USER nodejs

# Expor porta (Cloud Run define dinamicamente)
EXPOSE 8080

# Definir variáveis de ambiente
ENV NODE_ENV=production
ENV PORT=8080

# Copiar arquivo de configuração de produção se existir
COPY .env.production* ./

# Health check (removido para Cloud Run - ele tem seu próprio sistema)

# Comando para iniciar a aplicação
CMD ["npm", "start"]
