
import React, { useState, useRef, useEffect } from 'react';
import {
  Menu,
  Search,
  Bell,
  Settings,
  LogOut,
  Sun,
  Moon,
  ChevronDown
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { UserSettings } from './UserSettings';

interface HeaderProps {
  onToggleSidebar?: () => void;
  darkMode: boolean;
  onToggleDarkMode: () => void;
}

const Header: React.FC<HeaderProps> = ({ onToggleSidebar, darkMode, onToggleDarkMode }) => {
  const { user, logout, updateUser } = useAuth();
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const profileMenuRef = useRef<HTMLDivElement>(null);

  // Fechar menu ao clicar fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (profileMenuRef.current && !profileMenuRef.current.contains(event.target as Node)) {
        setIsProfileMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    logout();
    setIsProfileMenuOpen(false);
  };

  const handleSettingsClick = () => {
    setIsSettingsOpen(true);
    setIsProfileMenuOpen(false);
  };

  const handleUserUpdate = (updatedUser: any) => {
    updateUser(updatedUser);
  };

  return (
    <>
      <header className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-b shadow-sm`}>
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Lado Esquerdo */}
            <div className="flex items-center space-x-4">
              {/* Botão do Menu (Mobile) */}
              {onToggleSidebar && (
                <button
                  aria-label='Toggle Sidebar'
                  onClick={onToggleSidebar}
                  className={`p-2 rounded-md lg:hidden ${
                    darkMode 
                      ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <Menu className="h-6 w-6" />
                </button>
              )}

              {/* Logo/Título */}
              <div className="flex items-center">
                <h1 className={`text-xl font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  Dashboard
                </h1>
              </div>
            </div>

            {/* Centro - Barra de Pesquisa */}
            <div className="hidden md:flex flex-1 max-w-md mx-8">
              <div className="relative w-full">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className={`h-5 w-5 ${darkMode ? 'text-gray-400' : 'text-gray-400'}`} />
                </div>
                <input
                  type="text"
                  placeholder="Pesquisar..."
                  className={`block w-full pl-10 pr-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  }`}
                />
              </div>
            </div>

            {/* Lado Direito */}
            <div className="flex items-center space-x-4">
              {/* Botão Dark Mode */}
              <button
                onClick={onToggleDarkMode}
                className={`p-2 rounded-md ${
                  darkMode
                    ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                }`}
              >
                {darkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
              </button>

              {/* Notificações - Teste Simples */}
              <div className="relative">
                <button
                  type="button"
                  onClick={() => {
                    console.log('🔔 CLIQUE NO SINO DETECTADO!');
                    alert('Sino clicado! Sistema de notificações funcionando!');
                  }}
                  className={`p-2 rounded-md relative ${
                    darkMode
                      ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <Bell className="h-5 w-5" />
                  <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    3
                  </span>
                </button>
              </div>

              {/* Menu do Usuário */}
              <div className="relative" ref={profileMenuRef}>
                <button
                  onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
                  className={`flex items-center space-x-3 p-2 rounded-md ${
                    darkMode
                      ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                      : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  {/* Avatar */}
                  <div className="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                    <span className="text-sm font-medium text-white">
                      {user?.name?.charAt(0).toUpperCase() || 'U'}
                    </span>
                  </div>
                  
                  {/* Nome do usuário (oculto em mobile) */}
                  <div className="hidden md:block">
                    <div className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                      {user?.name || 'Usuário'}
                    </div>
                    <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      {user?.profile?.position || user?.role || 'Usuário'}
                    </div>
                  </div>
                  
                  <ChevronDown className="h-4 w-4" />
                </button>

                {/* Dropdown Menu */}
                {isProfileMenuOpen && (
                  <div className={`absolute right-0 mt-2 w-64 rounded-md shadow-lg z-50 ${
                    darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
                  } border`}>
                    <div className="py-1">
                      {/* Informações do usuário */}
                      <div className={`px-4 py-3 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                        <div className="flex items-center space-x-3">
                          <div className="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center">
                            <span className="text-sm font-medium text-white">
                              {user?.name?.charAt(0).toUpperCase() || 'U'}
                            </span>
                          </div>
                          <div>
                            <div className={`text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                              {user?.name || 'Usuário'}
                            </div>
                            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                              {user?.email || '<EMAIL>'}
                            </div>
                            <div className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                              {user?.profile?.position || user?.role || 'Usuário'}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Opções do menu */}
                    
                      
                      <button
                        onClick={handleSettingsClick}
                        className={`w-full flex items-center px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 ${
                          darkMode ? 'text-gray-300' : 'text-gray-700'
                        }`}
                      >
                        <Settings className="h-4 w-4 mr-3" />
                        Configurações
                      </button>
                      
                      <div className={`border-t ${darkMode ? 'border-gray-700' : 'border-gray-200'} my-1`}></div>
                      
                      <button
                        onClick={handleLogout}
                        className={`w-full flex items-center px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 ${
                          darkMode ? 'text-gray-300' : 'text-gray-700'
                        }`}
                      >
                        <LogOut className="h-4 w-4 mr-3" />
                        Sair
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Modal de Configurações */}
      {isSettingsOpen && user && (
        <UserSettings
          isOpen={isSettingsOpen}
          onClose={() => setIsSettingsOpen(false)}
          user={{
            ...user,
            lastLogin: user.lastLogin || undefined,
            createdAt: user.createdAt || new Date().toISOString()
          }}
          onUserUpdate={handleUserUpdate}
        />
      )}
    </>
  );
};

export default Header;Header;



