import 'dotenv/config';
import express from 'express';
import Strip<PERSON> from 'stripe';
import { body, validationResult } from 'express-validator';
import { authenticateToken, requireAdmin } from '../middleware/auth.js';
import User from '../models/User.js';
import AuthLogger from '../utils/authLogger.js';
import StripeImportService from '../services/stripeImportService.js';
import multer from 'multer';
import path from 'path';
import fs from 'fs-extra';

const router = express.Router();

// Configurar multer para upload de arquivos
const upload = multer({
  dest: 'uploads/',
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'text/csv' || file.originalname.endsWith('.csv')) {
      cb(null, true);
    } else {
      cb(new Error('Apenas arquivos CSV são permitidos'), false);
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  }
});

// Debug: verificar se a chave está sendo carregada
console.log('STRIPE_SECRET_KEY:', process.env.STRIPE_SECRET_KEY ? 'Carregada' : 'NÃO ENCONTRADA');

// Inicializar Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || 'sk_test_default', {
  apiVersion: '2023-10-16',
});

// Configuração de preços usando IDs reais do Stripe
const STRIPE_PRICE_IDS = {
  basic: {
    monthly: 'price_1RY5DHClUIoqY19kkU0D8pmf', // R$ 169,90
    yearly: 'price_1RYy8uClUIoqY19kuxks4wzT', // R$ 1.834,92 (economia de 20%)
  },
  standard: {
    monthly: 'price_1RY5FSClUIoqY19kh9kieCaY', // R$ 259,90
    yearly: 'price_1RYy8vClUIoqY19kRoJn3izD', // R$ 2.807,08 (economia de 20%)
  },
  professional: {
    monthly: 'price_1RY5GJClUIoqY19k1bCNt4lG', // R$ 599,90
    yearly: 'price_1RYy8vClUIoqY19kKdVt5jEo', // R$ 6.478,92 (economia de 20%)
  }
};

// Configuração de preços para exibição (em centavos)
const PRICE_CONFIG = {
  basic: {
    monthly: 16990, // R$ 169,90
    yearly: 183492,  // R$ 1.834,92 (economia de 20%)
  },
  standard: {
    monthly: 25990, // R$ 259,90
    yearly: 280708, // R$ 2.807,08 (economia de 20%)
  },
  professional: {
    monthly: 59990, // R$ 599,90
    yearly: 647892, // R$ 6.478,92 (economia de 20%)
  }
};

/**
 * Criar sessão de checkout do Stripe
 */
router.post('/create-checkout-session', [
  body('planType').isIn(['basic', 'standard', 'professional']).withMessage('Tipo de plano inválido'),
  body('billingCycle').isIn(['monthly', 'yearly']).withMessage('Ciclo de cobrança inválido'),
  body('successUrl').optional().isURL().withMessage('URL de sucesso inválida'),
  body('cancelUrl').optional().isURL().withMessage('URL de cancelamento inválida')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const {
      planType,
      billingCycle,
      leadData,
      successUrl = `${req.protocol}://${req.get('host')}/landingpage/success.html`,
      cancelUrl = `${req.protocol}://${req.get('host')}/landingpage/index.html`
    } = req.body;

    // Criar customer se leadData fornecido
    let customer = null;
    if (leadData && leadData.email) {
      customer = await stripe.customers.create({
        email: leadData.email,
        name: leadData.name,
        phone: leadData.phone,
        metadata: {
          organization: leadData.organization || '',
          source: leadData.source || 'landing_page',
          utm_source: leadData.utm?.utm_source || '',
          utm_campaign: leadData.utm?.utm_campaign || ''
        }
      });
    }

    // Verificar se o preço existe no Stripe
    const priceId = STRIPE_PRICE_IDS[planType]?.[billingCycle];
    if (!priceId) {
      return res.status(400).json({
        success: false,
        message: `Preço não encontrado para plano ${planType} com ciclo ${billingCycle}`
      });
    }

    // Criar sessão de checkout usando o ID do preço
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      mode: 'subscription',
      customer: customer?.id,
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      success_url: `${successUrl}?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: cancelUrl,
      metadata: {
        planType,
        billingCycle,
        source: 'landing_page'
      },
      subscription_data: {
        metadata: {
          planType,
          billingCycle
        }
      }
    });

    // Log da criação da sessão
    AuthLogger.log('stripe_checkout_created', {
      sessionId: session.id,
      planType,
      billingCycle,
      amount: PRICE_CONFIG[planType][billingCycle],
      customerId: customer?.id
    });

    res.json({
      success: true,
      sessionId: session.id,
      url: session.url
    });

  } catch (error) {
    console.error('Erro ao criar sessão de checkout:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * Criar Payment Intent para pagamentos únicos
 */
router.post('/create-payment-intent', authenticateToken, [
  body('planId').isIn(['basic', 'standard', 'professional']).withMessage('Plano inválido'),
  body('billingCycle').isIn(['monthly', 'yearly']).withMessage('Ciclo de cobrança inválido')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { planId, billingCycle } = req.body;
    const userId = req.user.id;

    // Buscar usuário
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuário não encontrado'
      });
    }

    // Criar ou buscar customer no Stripe
    let customer;
    if (user.stripeCustomerId) {
      customer = await stripe.customers.retrieve(user.stripeCustomerId);
    } else {
      customer = await stripe.customers.create({
        email: user.email,
        name: user.name,
        metadata: {
          userId: userId,
          organizationId: user.organizationId || ''
        }
      });

      // Salvar customer ID no usuário
      await User.updateById(userId, { stripeCustomerId: customer.id });
    }

    // Verificar se o preço existe no Stripe
    const priceId = STRIPE_PRICE_IDS[planId]?.[billingCycle];
    if (!priceId) {
      return res.status(400).json({
        success: false,
        message: `Preço não encontrado para plano ${planId} com ciclo ${billingCycle}`
      });
    }

    // Criar subscription usando o ID do preço
    const subscription = await stripe.subscriptions.create({
      customer: customer.id,
      items: [
        {
          price: priceId,
        },
      ],
      payment_behavior: 'default_incomplete',
      payment_settings: { save_default_payment_method: 'on_subscription' },
      expand: ['latest_invoice.payment_intent'],
      metadata: {
        userId,
        planId,
        billingCycle
      }
    });

    // Log da criação da subscription
    AuthLogger.log('stripe_subscription_created', {
      subscriptionId: subscription.id,
      userId,
      planId,
      billingCycle,
      amount: PRICE_CONFIG[planId][billingCycle]
    });

    res.json({
      success: true,
      subscriptionId: subscription.id,
      clientSecret: subscription.latest_invoice.payment_intent.client_secret
    });

  } catch (error) {
    console.error('Erro ao criar payment intent:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * Criar portal do cliente
 */
router.post('/create-customer-portal', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { returnUrl } = req.body;

    // Buscar usuário
    const user = await User.findById(userId);
    if (!user || !user.stripeCustomerId) {
      return res.status(404).json({
        success: false,
        message: 'Cliente Stripe não encontrado'
      });
    }

    // Criar sessão do portal
    const portalSession = await stripe.billingPortal.sessions.create({
      customer: user.stripeCustomerId,
      return_url: returnUrl || `${process.env.FRONTEND_URL}/dashboard`,
    });

    res.json({
      success: true,
      url: portalSession.url
    });

  } catch (error) {
    console.error('Erro ao criar portal do cliente:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * Verificar status de pagamento
 */
router.get('/check-payment-status/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params;

    const session = await stripe.checkout.sessions.retrieve(sessionId);

    res.json({
      success: true,
      status: session.payment_status,
      customerEmail: session.customer_details?.email,
      amountTotal: session.amount_total,
      currency: session.currency
    });

  } catch (error) {
    console.error('Erro ao verificar status do pagamento:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * Webhook do Stripe
 */
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  const sig = req.headers['stripe-signature'];
  const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    console.error('Erro na verificação do webhook:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Processar eventos do Stripe
  try {
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutCompleted(event.data.object);
        break;
      
      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object);
        break;
      
      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object);
        break;
      
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object);
        break;
      
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;
      
      default:
        console.log(`Evento não tratado: ${event.type}`);
    }

    res.json({ received: true });

  } catch (error) {
    console.error('Erro ao processar webhook:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Funções auxiliares para processar webhooks
async function handleCheckoutCompleted(session) {
  console.log('Checkout completed:', session.id);
  
  // Atualizar usuário com informações da subscription
  if (session.metadata?.userId) {
    await User.updateById(session.metadata.userId, {
      planType: session.metadata.planType,
      subscriptionStatus: 'active',
      stripeSubscriptionId: session.subscription
    });
  }

  AuthLogger.log('stripe_checkout_completed', {
    sessionId: session.id,
    customerId: session.customer,
    subscriptionId: session.subscription
  });
}

async function handlePaymentSucceeded(invoice) {
  console.log('Payment succeeded:', invoice.id);
  
  AuthLogger.log('stripe_payment_succeeded', {
    invoiceId: invoice.id,
    subscriptionId: invoice.subscription,
    amount: invoice.amount_paid
  });
}

async function handlePaymentFailed(invoice) {
  console.log('Payment failed:', invoice.id);
  
  AuthLogger.log('stripe_payment_failed', {
    invoiceId: invoice.id,
    subscriptionId: invoice.subscription,
    amount: invoice.amount_due
  });
}

async function handleSubscriptionUpdated(subscription) {
  console.log('Subscription updated:', subscription.id);
  
  // Atualizar status da subscription no banco
  if (subscription.metadata?.userId) {
    await User.updateById(subscription.metadata.userId, {
      subscriptionStatus: subscription.status
    });
  }
}

async function handleSubscriptionDeleted(subscription) {
  console.log('Subscription deleted:', subscription.id);
  
  // Atualizar usuário para plano gratuito
  if (subscription.metadata?.userId) {
    await User.updateById(subscription.metadata.userId, {
      planType: 'basic',
      subscriptionStatus: 'canceled',
      stripeSubscriptionId: null
    });
  }
}

/**
 * Importar dados do CSV do Stripe
 */
router.post('/import-csv', authenticateToken, requireAdmin, upload.single('csvFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Arquivo CSV é obrigatório'
      });
    }

    const csvFilePath = req.file.path;
    const importService = new StripeImportService();

    console.log(`Iniciando importação do arquivo: ${req.file.originalname}`);

    // Executar importação
    const result = await importService.importFromCSV(csvFilePath);

    // Limpar arquivo temporário
    await fs.remove(csvFilePath);

    // Log da importação
    AuthLogger.log('stripe_csv_import', {
      userId: req.user.id,
      fileName: req.file.originalname,
      stats: result.stats,
      success: result.success
    });

    if (result.success) {
      res.json({
        success: true,
        message: 'Importação concluída com sucesso',
        stats: result.stats,
        errors: result.errors
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Erro na importação',
        error: result.error,
        stats: result.stats
      });
    }

  } catch (error) {
    console.error('Erro na importação do CSV:', error);

    // Limpar arquivo temporário em caso de erro
    if (req.file) {
      await fs.remove(req.file.path).catch(() => {});
    }

    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * Listar relatórios de importação
 */
router.get('/import-reports', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const reportsPath = path.join(process.cwd(), 'backend/data/import-reports');

    if (!await fs.pathExists(reportsPath)) {
      return res.json({
        success: true,
        reports: []
      });
    }

    const files = await fs.readdir(reportsPath);
    const reports = [];

    for (const file of files) {
      if (file.endsWith('.json')) {
        try {
          const filePath = path.join(reportsPath, file);
          const content = await fs.readFile(filePath, 'utf8');
          const report = JSON.parse(content);

          reports.push({
            filename: file,
            timestamp: report.timestamp,
            stats: report.stats
          });
        } catch (error) {
          console.error(`Erro ao ler relatório ${file}:`, error);
        }
      }
    }

    // Ordenar por data (mais recente primeiro)
    reports.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    res.json({
      success: true,
      reports
    });

  } catch (error) {
    console.error('Erro ao listar relatórios:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

/**
 * Baixar relatório específico
 */
router.get('/import-reports/:filename', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { filename } = req.params;
    const filePath = path.join(process.cwd(), 'backend/data/import-reports', filename);

    if (!await fs.pathExists(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'Relatório não encontrado'
      });
    }

    const content = await fs.readFile(filePath, 'utf8');
    const report = JSON.parse(content);

    res.json({
      success: true,
      report
    });

  } catch (error) {
    console.error('Erro ao baixar relatório:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

export default router;
