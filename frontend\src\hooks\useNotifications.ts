import { useState, useEffect, useCallback, useRef } from 'react';
import { notificationService, Notification, NotificationFilters } from '../services/notificationService';

interface UseNotificationsReturn {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchNotifications: (filters?: NotificationFilters) => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAsUnread: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
  refreshUnreadCount: () => Promise<void>;
  
  // Filters
  getUnreadNotifications: () => Notification[];
  getNotificationsByCategory: (category: string) => Notification[];
  getNotificationsByType: (type: string) => Notification[];
}

interface UseNotificationsOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  initialFilters?: NotificationFilters;
  enablePolling?: boolean;
}

export const useNotifications = (options: UseNotificationsOptions = {}): UseNotificationsReturn => {
  const {
    autoRefresh = true,
    refreshInterval = 30000, // 30 segundos
    initialFilters,
    enablePolling = true
  } = options;

  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastCheckTimeRef = useRef<string>(new Date().toISOString());

  // Buscar notificações
  const fetchNotifications = useCallback(async (filters?: NotificationFilters) => {
    try {
      setLoading(true);
      setError(null);

      const response = await notificationService.getNotifications(filters || initialFilters);
      
      if (response.success && response.data) {
        setNotifications(response.data);
      } else {
        setError(response.error || 'Erro ao buscar notificações');
      }
    } catch (err) {
      setError('Erro inesperado ao buscar notificações');
      console.error('Erro ao buscar notificações:', err);
    } finally {
      setLoading(false);
    }
  }, [initialFilters]);

  // Atualizar contagem de não lidas
  const refreshUnreadCount = useCallback(async () => {
    try {
      const response = await notificationService.getUnreadCount();
      
      if (response.success && response.data) {
        setUnreadCount(response.data.count);
      }
    } catch (err) {
      console.error('Erro ao buscar contagem de não lidas:', err);
    }
  }, []);

  // Marcar como lida
  const markAsRead = useCallback(async (id: string) => {
    try {
      const response = await notificationService.markAsRead(id);
      
      if (response.success) {
        // Atualizar estado local
        setNotifications(prev => 
          prev.map(notification => 
            notification.id === id 
              ? { ...notification, read: true, readAt: new Date().toISOString() }
              : notification
          )
        );
        
        // Atualizar contagem
        setUnreadCount(prev => Math.max(0, prev - 1));
      } else {
        setError(response.error || 'Erro ao marcar como lida');
      }
    } catch (err) {
      setError('Erro inesperado ao marcar como lida');
      console.error('Erro ao marcar como lida:', err);
    }
  }, []);

  // Marcar como não lida
  const markAsUnread = useCallback(async (id: string) => {
    try {
      const response = await notificationService.markAsUnread(id);
      
      if (response.success) {
        // Atualizar estado local
        setNotifications(prev => 
          prev.map(notification => 
            notification.id === id 
              ? { ...notification, read: false, readAt: undefined }
              : notification
          )
        );
        
        // Atualizar contagem
        setUnreadCount(prev => prev + 1);
      } else {
        setError(response.error || 'Erro ao marcar como não lida');
      }
    } catch (err) {
      setError('Erro inesperado ao marcar como não lida');
      console.error('Erro ao marcar como não lida:', err);
    }
  }, []);

  // Marcar todas como lidas
  const markAllAsRead = useCallback(async () => {
    try {
      const response = await notificationService.markAllAsRead();
      
      if (response.success) {
        // Atualizar estado local
        const now = new Date().toISOString();
        setNotifications(prev => 
          prev.map(notification => ({
            ...notification,
            read: true,
            readAt: now
          }))
        );
        
        // Zerar contagem
        setUnreadCount(0);
      } else {
        setError(response.error || 'Erro ao marcar todas como lidas');
      }
    } catch (err) {
      setError('Erro inesperado ao marcar todas como lidas');
      console.error('Erro ao marcar todas como lidas:', err);
    }
  }, []);

  // Deletar notificação
  const deleteNotification = useCallback(async (id: string) => {
    try {
      const response = await notificationService.deleteNotification(id);
      
      if (response.success) {
        // Remover do estado local
        const notificationToDelete = notifications.find(n => n.id === id);
        setNotifications(prev => prev.filter(notification => notification.id !== id));
        
        // Atualizar contagem se era não lida
        if (notificationToDelete && !notificationToDelete.read) {
          setUnreadCount(prev => Math.max(0, prev - 1));
        }
      } else {
        setError(response.error || 'Erro ao deletar notificação');
      }
    } catch (err) {
      setError('Erro inesperado ao deletar notificação');
      console.error('Erro ao deletar notificação:', err);
    }
  }, [notifications]);

  // Filtros locais
  const getUnreadNotifications = useCallback(() => {
    return notifications.filter(notification => !notification.read);
  }, [notifications]);

  const getNotificationsByCategory = useCallback((category: string) => {
    return notifications.filter(notification => notification.category === category);
  }, [notifications]);

  const getNotificationsByType = useCallback((type: string) => {
    return notifications.filter(notification => notification.type === type);
  }, [notifications]);

  // Verificar novas notificações (polling)
  const checkForNewNotifications = useCallback(async () => {
    try {
      const response = await notificationService.checkForNewNotifications(lastCheckTimeRef.current);
      
      if (response.success && response.data && response.data.length > 0) {
        // Adicionar novas notificações ao início da lista
        setNotifications(prev => [...response.data!, ...prev]);
        
        // Atualizar contagem de não lidas
        const newUnreadCount = response.data.filter(n => !n.read).length;
        setUnreadCount(prev => prev + newUnreadCount);
        
        // Atualizar timestamp da última verificação
        lastCheckTimeRef.current = new Date().toISOString();
      }
    } catch (err) {
      console.error('Erro ao verificar novas notificações:', err);
    }
  }, []);

  // Configurar polling automático
  useEffect(() => {
    if (autoRefresh && enablePolling) {
      intervalRef.current = setInterval(() => {
        checkForNewNotifications();
        refreshUnreadCount();
      }, refreshInterval);

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  }, [autoRefresh, enablePolling, refreshInterval, checkForNewNotifications, refreshUnreadCount]);

  // Buscar dados iniciais
  useEffect(() => {
    fetchNotifications();
    refreshUnreadCount();
  }, [fetchNotifications, refreshUnreadCount]);

  // Limpar erro após 5 segundos
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        setError(null);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [error]);

  return {
    notifications,
    unreadCount,
    loading,
    error,
    
    // Actions
    fetchNotifications,
    markAsRead,
    markAsUnread,
    markAllAsRead,
    deleteNotification,
    refreshUnreadCount,
    
    // Filters
    getUnreadNotifications,
    getNotificationsByCategory,
    getNotificationsByType
  };
};
